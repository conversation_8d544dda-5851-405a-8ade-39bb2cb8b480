# 测试环境部署配置说明

## 概述

`s.staging.yaml` 文件已更新为使用新的强类型 payload 格式，符合 `tasks/README.md` 中定义的规范。

## 配置特点

### ✅ 强类型 Payload 格式
- 使用标准的 Function Compute 事件结构
- 包含 `triggerTime`、`triggerName` 和 `payload` 字段
- `payload.event_name` 用于自动识别任务类型
- `payload.event_params` 包含强类型验证的任务参数

### ✅ 定时任务配置

#### 启用的任务（4个）
1. **趋势刷新任务** - 每小时执行
   - 触发器：`trend-refresh-hourly`
   - 时间：每小时整点 (UTC)
   - 批处理：100个视频
   - 数据过期：1小时

2. **大批量趋势刷新** - 每6小时执行
   - 触发器：`trend-refresh-large`
   - 时间：0, 6, 12, 18点 (UTC)
   - 批处理：200个视频
   - 数据过期：6小时

3. **作者监控任务** - 每小时第15分钟执行
   - 触发器：`author-monitor-hourly`
   - 时间：每小时15分 (UTC)
   - 批处理：50个作者
   - 视频限制：每作者50个视频

4. **关键词监控任务** - 每小时第30分钟执行
   - 触发器：`keyword-monitor-hourly`
   - 时间：每小时30分 (UTC)
   - 批处理：20个关键词
   - 视频限制：每关键词100个视频

#### 禁用的任务（3个）
1. **深度趋势刷新** - 每天凌晨2点（北京时间）
   - 触发器：`trend-refresh-deep`
   - 批处理：500个视频
   - 包含日期范围过滤

2. **大批量作者监控** - 每天凌晨1点（北京时间）
   - 触发器：`author-monitor-large`
   - 批处理：100个作者

3. **周度关键词监控** - 每周日凌晨3点（北京时间）
   - 触发器：`keyword-monitor-weekly`
   - 搜索范围：14天

## Payload 示例

### 趋势刷新任务
```json
{
  "triggerTime": "2025-08-04T10:00:00Z",
  "triggerName": "trend-refresh-hourly",
  "payload": {
    "event_name": "trend_refresh_task",
    "event_params": {
      "task_type": "trend_refresh",
      "batch_size": 100,
      "max_age_hours": 1,
      "timeout": 3600
    }
  }
}
```

### 作者监控任务
```json
{
  "triggerTime": "2025-08-04T10:15:00Z",
  "triggerName": "author-monitor-hourly",
  "payload": {
    "event_name": "author_monitor_task",
    "event_params": {
      "task_type": "author_monitor",
      "batch_size": 50,
      "max_age_hours": 2,
      "timeout": 3600,
      "author_video_limit": 50
    }
  }
}
```

### 关键词监控任务
```json
{
  "triggerTime": "2025-08-04T10:30:00Z",
  "triggerName": "keyword-monitor-hourly",
  "payload": {
    "event_name": "keyword_monitor_task",
    "event_params": {
      "task_type": "keyword_monitor",
      "batch_size": 20,
      "max_age_hours": 3,
      "timeout": 3600,
      "keyword_video_limit": 100,
      "keyword_search_days": 7
    }
  }
}
```

## 部署说明

### 启用/禁用任务
要启用或禁用特定任务，修改对应触发器的 `enable` 字段：

```yaml
triggers:
  - triggerName: trend-refresh-deep
    triggerType: timer
    triggerConfig:
      cronExpression: "0 0 18 * * *"
      enable: true  # 改为 true 启用，false 禁用
```

### 调整任务参数
直接修改 `payload` 中的 `event_params` 参数：

```yaml
payload: |
  {
    "payload": {
      "event_params": {
        "batch_size": 200,     # 调整批处理大小
        "max_age_hours": 2,    # 调整数据过期时间
        "timeout": 7200        # 调整超时时间
      }
    }
  }
```

### 时区说明
- 所有 cron 表达式使用 UTC 时间
- 北京时间 = UTC 时间 + 8小时
- 例如：北京时间凌晨2点 = UTC时间18点

## 验证配置

运行以下命令验证配置格式：

```bash
# 验证 YAML 语法
python -c "import yaml; yaml.safe_load(open('s.staging.yaml'))"

# 验证 payload 格式（需要先创建测试脚本）
python test_staging_yaml.py
```

## 监控和日志

- 函数执行日志：阿里云 Function Compute 控制台
- 任务执行状态：通过 TaskResult 返回值查看
- 错误监控：通过日志和函数监控指标

## 注意事项

1. **资源配置**：任务函数配置了1GB内存和15分钟超时
2. **网络配置**：使用VPC网络访问数据库
3. **环境变量**：移除了硬编码的默认配置，全部通过 payload 传递
4. **强类型验证**：所有参数都会进行 Pydantic 模型验证
