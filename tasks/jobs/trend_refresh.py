"""
趋势刷新任务

实现 TrendInsight 视频趋势分数自动刷新功能
"""

import asyncio
from datetime import datetime, timedelta
from typing import AsyncIterator, Dict, List, Union



from rpc.trendinsight import AsyncTrendInsightAPI, client_manager
from controllers.douyin.video.video_process_controller import video_process_controller
from models.trendinsight.models import TrendInsightVideo

from ..core.base import BaseTask
from ..core.logger import TaskLogger
from ..core.models import TaskConfig, TaskResult
from ..monitors.base import create_monitor, profile_operation


class TrendRefreshTask(BaseTask):
    """趋势刷新任务 - 实现视频趋势分数刷新逻辑"""

    def __init__(self, config: TaskConfig, logger: TaskLogger):
        """
        初始化趋势刷新任务

        Args:
            config: 任务配置
            logger: 日志记录器
        """
        super().__init__(config, logger)
        self.controller = video_process_controller
        self.monitor = create_monitor("trend_refresh")
        self.api_calls = 0

    async def validate_params(self) -> bool:
        """
        验证任务参数

        Returns:
            bool: 参数是否有效
        """
        try:
            # 检查 TrendInsight 客户端连接
            try:
                async_client = client_manager.create_async_client()
                trendinsight_client = AsyncTrendInsightAPI(async_client=async_client)
                is_available = await trendinsight_client.pong()
                if not is_available:
                    self.logger.log_error("TrendInsight 客户端不可用")
                    return False
            except Exception as e:
                self.logger.log_error(f"TrendInsight 客户端连接失败: {str(e)}")
                return False

            # 验证过滤条件
            filters = self.config.filters or {}
            if "video_ids" in filters:
                video_ids = filters["video_ids"]
                if not isinstance(video_ids, list) or not all(isinstance(vid, str) for vid in video_ids):
                    self.logger.log_error("video_ids 过滤条件必须是字符串列表")
                    return False

            return True

        except Exception as e:
            self.logger.log_error(f"参数验证失败: {str(e)}")
            return False

    async def execute(self) -> TaskResult:
        """
        执行趋势刷新任务

        Returns:
            TaskResult: 执行结果
        """
        start_time = datetime.now()
        processed_count = 0
        success_count = 0
        failed_count = 0
        errors = []

        try:
            self.logger.log_progress(0, message="开始查询过期视频数据")

            # 流式查询过期视频
            async for batch_videos in self._query_stale_videos_batch():
                if self.is_interrupted:
                    self.logger.log_warning("任务被中断，停止处理")
                    break

                # 批量处理视频
                batch_results = await self._process_video_batch(batch_videos)

                # 更新统计信息
                processed_count += len(batch_videos)
                success_count += batch_results["success_count"]
                failed_count += batch_results["failed_count"]
                errors.extend(batch_results["errors"])

                # 记录进度（每100条记录）
                if processed_count % 100 == 0 or processed_count < 100:
                    self.logger.log_progress(
                        processed_count,
                        message=f"已处理 {processed_count} 个视频，成功 {success_count}，失败 {failed_count}",
                    )

                    # 记录性能指标
                    self.monitor.log_performance_metrics(processed_count=processed_count, api_calls=self.api_calls)

                    # 检查内存使用
                    self.monitor.check_memory_threshold(threshold_mb=512.0)

            # 确定任务状态
            if processed_count == 0:
                status = "success"
                self.logger.log_progress(0, message="没有需要刷新的过期视频")
            elif failed_count == 0:
                status = "success"
            elif success_count > 0:
                status = "partial"
            else:
                status = "failed"

            # 生成性能报告
            performance_report = self.monitor.generate_performance_report(
                final_processed_count=processed_count, final_api_calls=self.api_calls
            )

            result = self._create_result(
                status=status,
                start_time=start_time,
                processed_count=processed_count,
                success_count=success_count,
                failed_count=failed_count,
                errors=errors,
            )

            # 添加性能报告到结果
            if hasattr(result, "performance"):
                result.performance = performance_report

            return result

        except Exception as e:
            error_msg = f"任务执行失败: {str(e)}"
            self.logger.log_error(error_msg)
            errors.append(error_msg)

            return self._create_result(
                status="failed",
                start_time=start_time,
                processed_count=processed_count,
                success_count=success_count,
                failed_count=failed_count,
                errors=errors,
            )

    async def _query_stale_videos_batch(self) -> AsyncIterator[List[TrendInsightVideo]]:
        """
        流式批量查询过期视频

        Yields:
            List[TrendInsightVideo]: 视频批次
        """
        try:
            # 计算过期时间阈值
            cutoff_time = datetime.now() - timedelta(hours=self.config.max_age_hours)

            # 构建查询条件
            query = TrendInsightVideo.filter(updated_at__lte=cutoff_time)

            # 应用过滤条件
            filters = self.config.filters or {}
            if "video_ids" in filters:
                video_ids = filters["video_ids"]
                query = query.filter(id__in=video_ids)

            if "date_range" in filters:
                date_range = filters["date_range"]
                if "start" in date_range:
                    start_date = datetime.fromisoformat(date_range["start"])
                    query = query.filter(created_at__gte=start_date)
                if "end" in date_range:
                    end_date = datetime.fromisoformat(date_range["end"])
                    query = query.filter(created_at__lte=end_date)

            # 分批查询
            offset = 0
            batch_size = self.config.batch_size

            while True:
                if self.is_interrupted:
                    break

                batch = await query.offset(offset).limit(batch_size).all()
                if not batch:
                    break

                yield batch
                offset += batch_size

                # 批次间短暂休息，避免数据库压力
                await asyncio.sleep(0.1)

        except Exception as e:
            self.logger.log_error(f"查询过期视频失败: {str(e)}")
            raise

    async def _process_video_batch(self, videos: List[TrendInsightVideo]) -> Dict[str, Union[int, List[str]]]:
        """
        批量处理视频趋势刷新

        Args:
            videos: 视频列表

        Returns:
            dict: 批次处理结果
        """
        success_count = 0
        failed_count = 0
        errors = []

        for video in videos:
            if self.is_interrupted:
                break

            try:
                success = await self._refresh_video_trend(video)
                if success:
                    success_count += 1
                else:
                    failed_count += 1
                    errors.append(f"刷新视频 {video.id} 趋势失败")

            except Exception as e:
                failed_count += 1
                error_msg = f"处理视频 {video.id} 异常: {str(e)}"
                errors.append(error_msg)
                self.logger.log_error(error_msg)

            # 避免 API 限流
            await asyncio.sleep(0.5)

        return {"success_count": success_count, "failed_count": failed_count, "errors": errors}

    async def _refresh_video_trend(self, video: TrendInsightVideo) -> bool:
        """
        刷新单个视频的趋势分数

        Args:
            video: 视频对象

        Returns:
            bool: 是否成功刷新
        """
        try:
            aweme_id = video.id

            # 生成查询日期范围（过去7天）
            end_date = datetime.now() - timedelta(days=2)  # 前天
            start_date = end_date - timedelta(days=7)

            start_date_str = start_date.strftime("%Y%m%d")
            end_date_str = end_date.strftime("%Y%m%d")

            # 调用 TrendInsight API 获取视频指数
            try:
                with profile_operation(self.monitor, f"get_item_index_exist_{aweme_id}"):
                    # 创建 TrendInsight 客户端
                    async_client = client_manager.create_async_client()
                    trendinsight_client = AsyncTrendInsightAPI(async_client=async_client)

                    # 使用默认的日期范围（最近7天）
                    from datetime import datetime, timedelta
                    end_date = datetime.now().strftime("%Y%m%d")
                    start_date = (datetime.now() - timedelta(days=7)).strftime("%Y%m%d")

                    response = await trendinsight_client.get_item_index_exist(
                        item_id=aweme_id,
                        start_date=start_date,
                        end_date=end_date
                    )
                    self.api_calls += 1

                if not response.is_success:
                    self.logger.log_warning(f"视频 {aweme_id} API 调用失败")
                    return False

                # 检查视频状态
                if response.item_status != "succeed":
                    self.logger.log_warning(f"视频 {aweme_id} 状态异常: {response.item_status}")
                    return False

                # 检查是否有指数数据
                item_index = response.item_index
                if item_index is None:
                    self.logger.log_warning(f"视频 {aweme_id} 未获取到有效指数数据")
                    return False

                try:
                    trend_score = float(item_index)
                except (ValueError, TypeError):
                    self.logger.log_warning(f"视频 {aweme_id} 指数值格式无效: {item_index}")
                    return False

                # 更新数据库
                video.trend_score = trend_score
                video.updated_at = datetime.now()
                await video.save()

                self.logger.log_debug(f"成功刷新视频 {aweme_id} 趋势分数: {trend_score}")
                return True

            except Exception as e:
                self.logger.log_error(f"调用 TrendInsight API 失败: {aweme_id}, 错误: {str(e)}")
                return False

        except Exception as e:
            self.logger.log_error(f"刷新视频趋势失败: {video.id}, 错误: {str(e)}")
            return False
