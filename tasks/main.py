#!/usr/bin/env python3
"""
任务系统主入口 - FastAPI 服务模式

专为 Function Compute 环境设计的 FastAPI 服务
"""

import asyncio
import json
import logging
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import uvicorn
from fastapi import FastAPI, Request
from fastapi.responses import J<PERSON><PERSON>esponse, PlainTextResponse
from tortoise import Tortoise

from settings.config import settings
from tasks.core.manager import TaskManager
from tasks.core.models import (
    AuthorMonitorParams,
    EventPayload,
    FunctionComputeContext,
    FunctionComputeEvent,
    GenericEventPayload,
    KeywordMonitorParams,
    TrendRefreshParams,
)
from tasks.jobs.trend_refresh import TrendRefreshTask
from tasks.monitors.author import AuthorMonitorTask
from tasks.monitors.keyword import KeywordMonitorTask

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


async def handler(event, context):
    """
    Function Compute 时间触发器处理器

    根据阿里云 Function Compute 文档：
    - event: 时间触发器事件，格式为 {"triggerTime": "2023-12-26T07:49:00Z", "triggerName": "timer-trigger", "payload": "custom-data"}
    - context: 运行时上下文，包含 request_id, function_name 等
    """
    try:
        # 验证和解析事件数据
        if isinstance(event, dict):
            fc_event = FunctionComputeEvent(**event)
        else:
            # 尝试将字符串解析为事件
            try:
                event_dict = json.loads(event) if isinstance(event, str) else {}
                fc_event = FunctionComputeEvent(**event_dict)
            except (json.JSONDecodeError, TypeError):
                fc_event = FunctionComputeEvent()

        # 验证和解析上下文数据
        if hasattr(context, "request_id"):
            fc_context = FunctionComputeContext(
                request_id=context.request_id,
                function_name=getattr(context, "function_name", "task-scheduler"),
                function_version=getattr(context, "function_version", "$LATEST"),
                service_name=getattr(context, "service_name", "default"),
                memory_limit_in_mb=str(getattr(context, "memory_limit_in_mb", "1024")),
                time_limit_in_ms=str(getattr(context, "time_limit_in_ms", "900000")),
            )
        else:
            fc_context = FunctionComputeContext(request_id=f"local-{datetime.now().timestamp()}")

        # 记录触发信息
        logger.info(f"Function triggered by timer at {datetime.now()}")
        logger.info(f"Event: {fc_event.model_dump_json()}")
        logger.info(f"Context: request_id={fc_context.request_id}")

        # 从事件中获取任务配置
        config = get_task_config_from_event(fc_event, fc_context)
        logger.info(f"Task config: {json.dumps(config, ensure_ascii=False)}")

        # 执行任务
        result = await execute_task(config)

        # 记录执行结果
        logger.info(f"Task completed: {result.status}")
        logger.info(
            f"Processed: {result.processed_count}, Success: {result.success_count}, Failed: {result.failed_count}"
        )

        # 返回结果给 Function Compute
        response = {
            "statusCode": 200,
            "body": result.model_dump(),  # 使用 Pydantic v2 的 model_dump()
            "headers": {"Content-Type": "application/json"},
        }

        return response

    except Exception as e:
        error_msg = f"Function execution failed: {str(e)}"
        logger.error(error_msg, exc_info=True)

        return {
            "statusCode": 500,
            "body": {"error": error_msg, "timestamp": datetime.now().isoformat()},
            "headers": {"Content-Type": "application/json"},
        }


def get_task_config_from_event(event: FunctionComputeEvent, context: FunctionComputeContext):
    """从时间触发器事件中提取任务配置"""
    # 默认配置
    default_config = {
        "task_type": "trend_refresh",
        "batch_size": int(os.environ.get("BATCH_SIZE", "100")),
        "timeout": int(os.environ.get("TIMEOUT", "3600")),
        "max_age_hours": int(os.environ.get("MAX_AGE_HOURS", "1")),
    }

    # 现在 payload 一定是 EventPayload 类型
    payload = event.payload
    logger.info(f"Processing event: {payload.event_name}")

    # 根据事件名称设置任务类型
    event_name = payload.event_name.lower()
    if "trend" in event_name or "refresh" in event_name:
        default_config["task_type"] = "trend_refresh"
    elif "author" in event_name or "monitor" in event_name:
        default_config["task_type"] = "author_monitor"
    elif "keyword" in event_name:
        default_config["task_type"] = "keyword_monitor"

    # 从事件参数中更新配置
    if payload.event_params:
        if isinstance(payload.event_params, dict):
            default_config.update(payload.event_params)
        elif hasattr(payload.event_params, "model_dump"):
            # 如果是 Pydantic 模型，转换为字典
            default_config.update(payload.event_params.model_dump())
        else:
            # 其他类型，尝试转换为字典
            try:
                if hasattr(payload.event_params, "__dict__"):
                    default_config.update(payload.event_params.__dict__)
            except Exception as e:
                logger.warning(f"Failed to extract event_params: {e}")

    # 从事件的 config 字段中获取配置
    if event.config:
        default_config.update(event.config)

    return default_config


async def execute_task(config_dict: Dict[str, Any]):
    """统一的任务执行逻辑"""
    try:
        # 初始化数据库连接
        await Tortoise.init(config=settings.tortoise_orm)

        # 创建任务管理器
        manager = TaskManager()

        # 注册任务类型
        manager.register_task("trend_refresh", TrendRefreshTask)
        manager.register_task("author_monitor", AuthorMonitorTask)
        manager.register_task("keyword_monitor", KeywordMonitorTask)

        # 验证配置
        try:
            task_config = manager.validate_config(config_dict)
        except ValueError as e:
            error_result = {
                "task_type": config_dict.get("task_type", "unknown"),
                "status": "failed",
                "error": "配置验证失败",
                "details": str(e),
                "timestamp": datetime.now().isoformat(),
            }
            return type("TaskResult", (), error_result)()

        # 执行任务
        try:
            result = await manager.execute_task(task_config)
            return result

        except TimeoutError as e:
            error_result = {
                "task_type": task_config.task_type,
                "status": "failed",
                "error": "任务执行超时",
                "details": str(e),
                "timestamp": datetime.now().isoformat(),
            }
            return type("TaskResult", (), error_result)()

        except Exception as e:
            error_result = {
                "task_type": task_config.task_type,
                "status": "failed",
                "error": "任务执行异常",
                "details": str(e),
                "timestamp": datetime.now().isoformat(),
            }
            return type("TaskResult", (), error_result)()

    finally:
        # 关闭数据库连接
        try:
            await Tortoise.close_connections()
        except:
            pass


async def start_fastapi_server():
    """启动 FastAPI 服务器用于 Function Compute"""
    app = FastAPI(title="Task Scheduler", version="1.0.0")

    @app.post("/invoke")
    async def invoke_task(request: Request):
        """Function Compute 调用接口 - 符合阿里云规范的 /invoke 路由"""
        try:
            headers = dict(request.headers)

            # 记录 Function Compute 请求信息
            request_id = headers.get("x-fc-request-id", f"local-{datetime.now().timestamp()}")
            logger.info(f"Function Compute invoke request: request_id={request_id}")

            # 创建 Function Compute context
            context = FunctionComputeContext(
                request_id=request_id,
                function_name=headers.get("x-fc-function-name", os.environ.get("FC_FUNCTION_NAME", "task-scheduler")),
                function_version=headers.get("x-fc-function-version", "$LATEST"),
                service_name=headers.get("x-fc-service-name", "default"),
                memory_limit_in_mb=headers.get("x-fc-memory-limit-in-mb", "1024"),
                time_limit_in_ms=headers.get("x-fc-time-limit-in-ms", "900000"),
            )

            # 解析请求体 - 支持 application/octet-stream 和 application/json
            body = await request.body()
            if body:
                content_type = headers.get("content-type", "application/json")
                if content_type == "application/octet-stream":
                    # Function Compute 标准格式
                    try:
                        event = json.loads(body.decode("utf-8"))
                    except json.JSONDecodeError:
                        # 如果不是 JSON，作为原始数据处理
                        event = {"data": body.decode("utf-8", errors="ignore")}
                else:
                    # 标准 JSON 格式
                    try:
                        event = json.loads(body.decode("utf-8"))
                    except json.JSONDecodeError as e:
                        logger.error(f"JSON decode error: {e}")
                        return PlainTextResponse(f"JSON parse error: {str(e)}", status_code=400)
            else:
                event = {}

            logger.info(f"Parsed event: {json.dumps(event, ensure_ascii=False)}")

            # 调用 Function Compute handler
            result = await handler(event, context)

            # 根据 Function Compute 规范返回结果
            if isinstance(result, dict) and "statusCode" in result:
                # 标准 Function Compute 响应格式
                status_code = result.get("statusCode", 200)
                response_body = result.get("body", {})

                # 确保响应体是可序列化的
                if isinstance(response_body, dict):
                    response_content = json.dumps(response_body, ensure_ascii=False, default=str)
                else:
                    response_content = str(response_body)

                return PlainTextResponse(
                    content=response_content, status_code=status_code, headers=result.get("headers", {})
                )
            else:
                # 简单响应
                return PlainTextResponse(str(result), status_code=200)

        except Exception as e:
            error_msg = f"Function invoke failed: {str(e)}"
            logger.error(error_msg, exc_info=True)

            # 返回 Function Compute 标准错误格式
            error_response = {"error": error_msg, "timestamp": datetime.now().isoformat()}
            return PlainTextResponse(content=json.dumps(error_response, ensure_ascii=False), status_code=500)

    @app.post("/")
    @app.get("/")
    async def function_handler(request: Request):
        """处理 Function Compute 根路由请求 - 兼容性处理"""
        try:
            headers = dict(request.headers)

            logger.info(f"Root path request: method={request.method}, url={request.url}")

            # 处理 Function Compute 初始化请求
            if headers.get("x-fc-control-path") == "/initialize":
                logger.info("Function Compute initialization request")
                return PlainTextResponse("", status_code=200)

            # 处理预热请求
            if headers.get("x-fc-control-path") == "/pre-freeze":
                logger.info("Function Compute pre-freeze request")
                return PlainTextResponse("", status_code=200)

            # 处理冻结后请求
            if headers.get("x-fc-control-path") == "/pre-stop":
                logger.info("Function Compute pre-stop request")
                return PlainTextResponse("", status_code=200)

            # GET 请求返回健康检查
            if request.method == "GET":
                logger.info("Health check request")
                return PlainTextResponse("OK", status_code=200)

            # POST 请求 - 如果不是 /invoke 路由，重定向到标准处理
            if request.method == "POST":
                logger.info("Root POST request - redirecting to standard handler")

                # 解析请求体
                body = await request.body()
                event = {}
                if body:
                    try:
                        event = json.loads(body.decode("utf-8"))
                    except json.JSONDecodeError:
                        logger.warning("Failed to parse request body as JSON")
                        event = {"raw_data": body.decode("utf-8", errors="ignore")}

                # 创建 Function Compute context
                context = FunctionComputeContext(
                    request_id=headers.get("x-fc-request-id", f"root-{datetime.now().timestamp()}"),
                    function_name=headers.get(
                        "x-fc-function-name", os.environ.get("FC_FUNCTION_NAME", "task-scheduler")
                    ),
                    function_version=headers.get("x-fc-function-version", "$LATEST"),
                    service_name=headers.get("x-fc-service-name", "default"),
                    memory_limit_in_mb=headers.get("x-fc-memory-limit-in-mb", "1024"),
                    time_limit_in_ms=headers.get("x-fc-time-limit-in-ms", "900000"),
                )

                # 调用处理函数
                result = await handler(event, context)

                # 返回结果
                if isinstance(result, dict) and "statusCode" in result:
                    return JSONResponse(content=result, status_code=200)
                else:
                    return PlainTextResponse(str(result), status_code=200)

            # 其他请求方法
            logger.warning(f"Unsupported method: {request.method}")
            return PlainTextResponse("Method not allowed", status_code=405)

        except Exception as e:
            logger.error(f"Root handler error: {str(e)}", exc_info=True)
            error_response = {
                "statusCode": 500,
                "body": {"error": str(e), "timestamp": datetime.now().isoformat()},
                "headers": {"Content-Type": "application/json"},
            }
            return JSONResponse(content=error_response, status_code=500)

    # 启动服务器
    port = int(os.environ.get("PORT", "8000"))
    logger.info(f"Task scheduler FastAPI server starting on port {port}")

    # 使用 uvicorn.Server 在当前事件循环中运行
    config = uvicorn.Config(app, host="0.0.0.0", port=port, log_level="info", access_log=False)
    server = uvicorn.Server(config)
    await server.serve()


async def main():
    """主函数 - 支持命令行模式和服务器模式"""
    if len(sys.argv) > 1:
        # 命令行模式：直接执行任务
        try:
            # 解析命令行参数中的 JSON 配置
            config_json = sys.argv[1]
            config_dict = json.loads(config_json)

            logger.info(f"命令行模式启动，任务配置: {json.dumps(config_dict, ensure_ascii=False)}")

            # 执行任务
            result = await execute_task(config_dict)

            # 输出结果
            logger.info(f"任务执行完成: {result.status}")
            logger.info(f"处理数量: {result.processed_count}, 成功: {result.success_count}, 失败: {result.failed_count}")

            # 根据执行结果设置退出码
            if result.status == "completed":
                sys.exit(0)
            elif result.status == "failed":
                sys.exit(1)
            else:
                sys.exit(2)

        except json.JSONDecodeError as e:
            logger.error(f"JSON 参数解析失败: {e}")
            logger.error(f"参数内容: {sys.argv[1] if len(sys.argv) > 1 else 'None'}")
            sys.exit(1)
        except Exception as e:
            logger.error(f"任务执行异常: {e}")
            sys.exit(1)
    else:
        # 服务器模式：启动 FastAPI 服务
        logger.info("服务器模式启动")
        await start_fastapi_server()


if __name__ == "__main__":
    asyncio.run(main())
