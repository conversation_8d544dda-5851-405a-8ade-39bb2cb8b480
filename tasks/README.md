# 任务系统文档

## 概述

本任务系统是一个独立的定时任务执行系统，支持多种类型的自动化任务，包括视频趋势分数刷新、作者监控和关键词监控。系统采用命令行方式调用，通过 JSON 参数配置任务类型和执行参数，支持流式批处理以避免内存问题，并提供完整的日志记录和监控功能。

## 主要特性

- ✅ **多任务类型**: 支持趋势刷新、作者监控、关键词监控等多种任务
- ✅ **独立运行**: 不依赖 Web 服务，可作为独立进程执行
- ✅ **JSON 配置**: 通过命令行参数传入 JSON 格式的任务配置
- ✅ **流式处理**: 支持大批量数据的流式批处理，避免内存溢出
- ✅ **完整日志**: 结构化 JSON 日志输出，包含进度追踪和错误记录
- ✅ **容错机制**: API 调用失败自动跳过，支持优雅中断
- ✅ **灵活配置**: 支持批处理大小、超时时间、过滤条件等参数配置
- ✅ **标准退出码**: 不同执行结果返回对应的退出码，便于外部调度器判断
- ✅ **全面测试**: 包含单元测试和集成测试，保证代码质量

## 支持的任务类型

### 1. 趋势刷新任务 (trend_refresh)
自动刷新 TrendInsight 视频的趋势分数

### 2. 作者监控任务 (author_monitor)
监控指定作者的新视频并获取趋势数据

### 3. 关键词监控任务 (keyword_monitor)
基于关键词搜索新视频并获取趋势数据

## 定时任务 Payload 示例

### 强类型 Payload 系统

本系统使用强类型的 Pydantic 模型来确保 payload 数据的类型安全和验证：

#### 强类型模型定义

```python
# 参数模型
class TrendRefreshParams(BaseModel):
    batch_size: int = 100
    max_age_hours: int = 1
    timeout: int = 3600
    filters: Optional[Dict[str, Any]] = None

class AuthorMonitorParams(BaseModel):
    batch_size: int = 50
    max_age_hours: int = 2
    timeout: int = 3600
    author_video_limit: int = 50
    filters: Optional[Dict[str, Any]] = None

class KeywordMonitorParams(BaseModel):
    batch_size: int = 20
    max_age_hours: int = 3
    timeout: int = 3600
    keyword_video_limit: int = 100
    keyword_search_days: int = 7
    filters: Optional[Dict[str, Any]] = None

# 强类型载荷
TrendRefreshPayload = EventPayload[TrendRefreshParams]
AuthorMonitorPayload = EventPayload[AuthorMonitorParams]
KeywordMonitorPayload = EventPayload[KeywordMonitorParams]
```

#### 自动类型解析

系统会根据 `event_name` 自动解析为对应的强类型 payload：

- `trend_refresh_task` → `TrendRefreshPayload`
- `author_monitor_task` → `AuthorMonitorPayload`
- `keyword_monitor_task` → `KeywordMonitorPayload`

#### 类型安全特性

✅ **编译时类型检查**: 使用 Pydantic 模型确保参数类型正确
✅ **运行时验证**: 自动验证参数范围和格式
✅ **IDE 支持**: 完整的类型提示和自动补全
✅ **错误提示**: 详细的参数验证错误信息

### Function Compute 时间触发器事件格式

所有定时任务都使用以下标准事件格式：

```json
{
  "triggerTime": "2025-08-04T10:00:00Z",
  "triggerName": "task-trigger-name",
  "payload": {
    "event_name": "任务事件名称",
    "event_params": {
      // 具体任务参数（会被解析为强类型模型）
    }
  }
}
```

### 1. 趋势刷新任务 Payload

#### 基础趋势刷新
```json
{
  "triggerTime": "2025-08-04T10:00:00Z",
  "triggerName": "trend-refresh-hourly",
  "payload": {
    "event_name": "trend_refresh_task",
    "event_params": {
      "task_type": "trend_refresh",
      "batch_size": 100,
      "max_age_hours": 1,
      "timeout": 3600
    }
  }
}
```

#### 大批量趋势刷新
```json
{
  "triggerTime": "2025-08-04T10:00:00Z",
  "triggerName": "trend-refresh-large",
  "payload": {
    "event_name": "trend_refresh_task",
    "event_params": {
      "task_type": "trend_refresh",
      "batch_size": 200,
      "max_age_hours": 6,
      "timeout": 7200
    }
  }
}
```

#### 指定视频ID的趋势刷新
```json
{
  "triggerTime": "2025-08-04T10:00:00Z",
  "triggerName": "trend-refresh-specific",
  "payload": {
    "event_name": "trend_refresh_task",
    "event_params": {
      "task_type": "trend_refresh",
      "batch_size": 50,
      "max_age_hours": 1,
      "timeout": 3600,
      "filters": {
        "video_ids": ["7532843935614487858", "7527997562239110440"]
      }
    }
  }
}
```

#### 按日期范围的趋势刷新
```json
{
  "triggerTime": "2025-08-04T10:00:00Z",
  "triggerName": "trend-refresh-date-range",
  "payload": {
    "event_name": "trend_refresh_task",
    "event_params": {
      "task_type": "trend_refresh",
      "batch_size": 100,
      "max_age_hours": 24,
      "timeout": 3600,
      "filters": {
        "date_range": {
          "start": "2025-08-01",
          "end": "2025-08-04"
        }
      }
    }
  }
}
```

### 2. 作者监控任务 Payload

#### 基础作者监控
```json
{
  "triggerTime": "2025-08-04T10:15:00Z",
  "triggerName": "author-monitor-hourly",
  "payload": {
    "event_name": "author_monitor_task",
    "event_params": {
      "task_type": "author_monitor",
      "batch_size": 50,
      "max_age_hours": 2,
      "timeout": 3600,
      "author_video_limit": 50
    }
  }
}
```

#### 大批量作者监控
```json
{
  "triggerTime": "2025-08-04T10:15:00Z",
  "triggerName": "author-monitor-large",
  "payload": {
    "event_name": "author_monitor_task",
    "event_params": {
      "task_type": "author_monitor",
      "batch_size": 100,
      "max_age_hours": 6,
      "timeout": 7200,
      "author_video_limit": 100
    }
  }
}
```

#### 指定作者的监控
```json
{
  "triggerTime": "2025-08-04T10:15:00Z",
  "triggerName": "author-monitor-specific",
  "payload": {
    "event_name": "author_monitor_task",
    "event_params": {
      "task_type": "author_monitor",
      "batch_size": 20,
      "max_age_hours": 1,
      "timeout": 3600,
      "author_video_limit": 30,
      "filters": {
        "author_ids": ["author123", "author456"]
      }
    }
  }
}
```

### 3. 关键词监控任务 Payload

#### 基础关键词监控
```json
{
  "triggerTime": "2025-08-04T10:30:00Z",
  "triggerName": "keyword-monitor-daily",
  "payload": {
    "event_name": "keyword_monitor_task",
    "event_params": {
      "task_type": "keyword_monitor",
      "batch_size": 20,
      "max_age_hours": 3,
      "timeout": 3600,
      "keyword_video_limit": 100,
      "keyword_search_days": 7
    }
  }
}
```

#### 大范围关键词监控
```json
{
  "triggerTime": "2025-08-04T10:30:00Z",
  "triggerName": "keyword-monitor-weekly",
  "payload": {
    "event_name": "keyword_monitor_task",
    "event_params": {
      "task_type": "keyword_monitor",
      "batch_size": 50,
      "max_age_hours": 24,
      "timeout": 7200,
      "keyword_video_limit": 200,
      "keyword_search_days": 14
    }
  }
}
```

#### 指定关键词的监控
```json
{
  "triggerTime": "2025-08-04T10:30:00Z",
  "triggerName": "keyword-monitor-specific",
  "payload": {
    "event_name": "keyword_monitor_task",
    "event_params": {
      "task_type": "keyword_monitor",
      "batch_size": 30,
      "max_age_hours": 6,
      "timeout": 3600,
      "keyword_video_limit": 50,
      "keyword_search_days": 3,
      "filters": {
        "keywords": ["科技", "AI", "人工智能"]
      }
    }
  }
}
```

## 任务参数说明

### 通用参数

| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `task_type` | string | ✅ | 任务类型：`trend_refresh`、`author_monitor`、`keyword_monitor` |
| `batch_size` | int | ❌ | 批处理大小，范围 1-1000 |
| `timeout` | int | ❌ | 任务超时时间（秒），范围 1-86400 |
| `max_age_hours` | int | ❌ | 数据过期时间（小时），范围 1-168 |
| `filters` | object | ❌ | 过滤条件对象 |

### 趋势刷新任务参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `batch_size` | int | 100 | 批处理大小 |
| `max_age_hours` | int | 1 | 数据过期时间（小时）|
| `timeout` | int | 3600 | 任务超时时间（秒）|

### 作者监控任务参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `batch_size` | int | 50 | 批处理大小 |
| `max_age_hours` | int | 2 | 数据过期时间（小时）|
| `timeout` | int | 3600 | 任务超时时间（秒）|
| `author_video_limit` | int | 50 | 每个作者最多获取的视频数量 |

### 关键词监控任务参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `batch_size` | int | 20 | 批处理大小 |
| `max_age_hours` | int | 3 | 数据过期时间（小时）|
| `timeout` | int | 3600 | 任务超时时间（秒）|
| `keyword_video_limit` | int | 100 | 每个关键词最多获取的视频数量 |
| `keyword_search_days` | int | 7 | 关键词搜索的天数范围 |

## 过滤条件

### 通用过滤条件

```json
{
  "filters": {
    "date_range": {
      "start": "2025-08-01",
      "end": "2025-08-04"
    }
  }
}
```

### 趋势刷新任务过滤条件

```json
{
  "filters": {
    "video_ids": ["7532843935614487858", "7527997562239110440"],
    "date_range": {
      "start": "2025-08-01",
      "end": "2025-08-04"
    }
  }
}
```

### 作者监控任务过滤条件

```json
{
  "filters": {
    "author_ids": ["author123", "author456"],
    "date_range": {
      "start": "2025-08-01",
      "end": "2025-08-04"
    }
  }
}
```

### 关键词监控任务过滤条件

```json
{
  "filters": {
    "keywords": ["科技", "AI", "人工智能"],
    "date_range": {
      "start": "2025-08-01",
      "end": "2025-08-04"
    }
  }
}
```

## 快速开始

### 命令行模式

```bash
# 趋势刷新任务
python tasks/main.py '{"task_type": "trend_refresh", "batch_size": 100, "max_age_hours": 1}'

# 作者监控任务
python tasks/main.py '{"task_type": "author_monitor", "batch_size": 50, "author_video_limit": 50}'

# 关键词监控任务
python tasks/main.py '{"task_type": "keyword_monitor", "batch_size": 20, "keyword_video_limit": 100}'
```

### Makefile 快捷命令

```bash
# 查看所有可用任务命令
make task-help

# 执行趋势刷新任务
make task-trend-refresh

# 执行作者监控任务
make task-author-monitor

# 执行关键词监控任务
make task-keyword-monitor
```

### 退出码说明

| 退出码 | 状态 | 说明 |
|--------|------|------|
| 0 | 成功 | 任务完全成功执行 |
| 1 | 部分成功 | 有部分视频处理失败，但任务整体完成 |
| 2 | 完全失败 | 任务执行失败，没有任何视频被成功处理 |
| 3 | 超时 | 任务执行超时 |
| 4 | 执行异常 | 任务执行过程中发生异常 |
| 5 | 系统错误 | 系统级别的错误 |
| 130 | 中断 | 用户键盘中断（Ctrl+C） |

### 日志输出

任务系统输出结构化的 JSON 日志，包含以下事件类型：

#### 任务开始
```json
{
  "event": "task_start",
  "task_type": "trend_refresh",
  "config": {...},
  "timestamp": "2025-01-21T10:00:00Z"
}
```

#### 进度记录
```json
{
  "event": "progress",
  "task_type": "trend_refresh",
  "processed": 50,
  "total": 100,
  "percentage": 50.0,
  "message": "已处理 50 个视频",
  "timestamp": "2025-01-21T10:05:00Z"
}
```

#### 任务完成
```json
{
  "event": "task_complete",
  "task_type": "trend_refresh",
  "status": "success",
  "duration": 300.5,
  "processed_count": 100,
  "success_count": 95,
  "failed_count": 5,
  "success_rate": 95.0,
  "timestamp": "2025-01-21T10:05:00Z"
}
```

#### 最终输出（标准输出）
```json
{
  "task_type": "trend_refresh",
  "status": "success",
  "start_time": "2025-01-21T10:00:00.000000",
  "end_time": "2025-01-21T10:05:00.500000",
  "duration": 300.5,
  "processed_count": 100,
  "success_count": 95,
  "failed_count": 5,
  "errors": ["API调用失败: 视频123", "超时: 视频456"]
}
```

## 部署方式

### 1. Function Compute 部署（推荐）

Function Compute 是阿里云提供的 Serverless 计算服务，支持按需运行、自动扩缩容，非常适合定时任务场景。

#### 快速部署

```bash
# 使用部署脚本
./scripts/deploy_tasks.sh

# 或者手动部署定时任务资源
s deploy -t s.staging.yaml task-scheduler
```

#### 配置说明

系统预配置了两个定时触发器：

1. **趋势刷新任务**: 每小时执行一次
   - CRON 表达式: `0 0 */1 * * *`
   - 批处理大小: 100
   - 适用于常规的数据刷新

2. **深度趋势刷新**: 每6小时执行一次
   - CRON 表达式: `0 0 0,6,12,18 * * *`
   - 批处理大小: 200
   - 适用于更全面的数据更新

#### 管理命令

```bash
# 查看部署信息
s info -t s.staging.yaml task-scheduler

# 查看函数日志
s logs -t s.staging.yaml task-scheduler --tail

# 手动测试函数
s invoke -t s.staging.yaml task-scheduler --event '{
  "triggerTime": "2025-01-21T10:00:00Z",
  "triggerName": "test-trigger",
  "payload": "{\"task_type\": \"trend_refresh\", \"batch_size\": 50}"
}'

# 更新函数代码
s deploy -t s.staging.yaml task-scheduler --use-local

# 删除函数
s remove -t s.staging.yaml task-scheduler
```

#### 优势

- ✅ **免维护**: 无需管理服务器，自动扩缩容
- ✅ **高可用**: 内置故障转移和重试机制
- ✅ **成本优化**: 按实际执行时间计费，空闲时无费用
- ✅ **监控完善**: 集成阿里云日志服务和监控
- ✅ **弹性伸缩**: 自动处理并发和负载波动

#### 环境变量配置

函数支持通过环境变量配置任务参数：

| 环境变量 | 默认值 | 说明 |
|---------|-------|------|
| `BATCH_SIZE` | 100 | 默认批处理大小 |
| `TIMEOUT` | 3600 | 默认任务超时时间（秒）|
| `MAX_AGE_HOURS` | 1 | 默认数据过期时间（小时）|
| `ENV_FOR_DYNACONF` | staging | 环境配置标识 |

#### 时间触发器事件格式

Function Compute 的时间触发器会传递以下格式的事件：

```json
{
  "triggerTime": "2025-01-21T10:00:00Z",
  "triggerName": "trend-refresh-hourly", 
  "payload": "{\"task_type\": \"trend_refresh\", \"batch_size\": 100}"
}
```

#### 自定义触发器配置

在 `s.staging.yaml` 中添加新的触发器：

```yaml
triggers:
  - triggerName: custom-task
    triggerType: timer
    triggerConfig:
      # 每天凌晨2点执行（UTC时间，对应北京时间10点）
      cronExpression: "0 0 2 * * *"
      enable: true
      payload: |
        {
          "task_type": "trend_refresh",
          "batch_size": 500,
          "timeout": 7200,
          "filters": {
            "deep_refresh": true
          }
        }
```

### 2. Cron 部署

```bash
# 每小时执行一次趋势刷新
0 */1 * * * cd /app && python tasks/main.py '{"task_type": "trend_refresh"}' >> /var/log/trend-refresh.log 2>&1

# 每天凌晨2点执行完整刷新
0 2 * * * cd /app && python tasks/main.py '{"task_type": "trend_refresh", "batch_size": 200, "timeout": 7200}' >> /var/log/trend-refresh-daily.log 2>&1
```

### 2. Kubernetes CronJob

```yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: trend-refresh-task
spec:
  schedule: "0 */1 * * *"  # 每小时执行
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: trend-refresh
            image: your-app:latest
            command: ["python", "tasks/main.py"]
            args: ['{"task_type": "trend_refresh", "batch_size": 100}']
            env:
            - name: DATABASE_URL
              value: "mysql://user:pass@host:3306/db"
            resources:
              requests:
                memory: "256Mi"
                cpu: "100m"
              limits:
                memory: "512Mi"
                cpu: "500m"
          restartPolicy: OnFailure
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 1
```

### 3. Docker 部署

```dockerfile
# Dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY . .
RUN pip install -r requirements.txt

# 任务执行入口
ENTRYPOINT ["python", "tasks/main.py"]
```

```bash
# 运行任务
docker run --rm your-app:latest '{"task_type": "trend_refresh", "batch_size": 50}'
```

### 4. Systemd 定时任务

```ini
# /etc/systemd/system/trend-refresh.service
[Unit]
Description=TrendInsight Video Refresh Task
Wants=trend-refresh.timer

[Service]
Type=oneshot
User=app
WorkingDirectory=/app
Environment=PYTHONPATH=/app
ExecStart=/usr/bin/python tasks/main.py {"task_type": "trend_refresh", "batch_size": 100}
StandardOutput=journal
StandardError=journal
```

```ini
# /etc/systemd/system/trend-refresh.timer
[Unit]
Description=Run trend refresh task every hour
Requires=trend-refresh.service

[Timer]
OnCalendar=hourly
Persistent=true

[Install]
WantedBy=timers.target
```

```bash
# 启用定时任务
sudo systemctl daemon-reload
sudo systemctl enable trend-refresh.timer
sudo systemctl start trend-refresh.timer
```

## 监控和告警

### 1. 日志监控

```bash
# 监控任务执行状态
tail -f /var/log/trend-refresh.log | jq 'select(.event == "task_complete")'

# 监控错误
tail -f /var/log/trend-refresh.log | jq 'select(.level == "ERROR")'
```

### 2. Prometheus 指标

系统日志可以通过 Promtail + Loki 收集并转换为 Prometheus 指标：

```yaml
# promtail config
scrape_configs:
- job_name: trend-refresh-logs
  static_configs:
  - targets:
      - localhost
    labels:
      job: trend-refresh
      __path__: /var/log/trend-refresh.log
```

### 3. 告警规则

```yaml
# alertmanager rules
groups:
- name: trend_refresh
  rules:
  - alert: TrendRefreshTaskFailed
    expr: rate(log_messages_total{job="trend-refresh", status="failed"}[5m]) > 0
    for: 0m
    labels:
      severity: warning
    annotations:
      summary: "趋势刷新任务失败"
      
  - alert: TrendRefreshTaskTimeout
    expr: rate(log_messages_total{job="trend-refresh", status="timeout"}[5m]) > 0
    for: 0m
    labels:
      severity: critical
    annotations:
      summary: "趋势刷新任务超时"
```

## 性能优化建议

### 1. 批处理大小调优

- **小数据量** (< 1000 条): `batch_size: 50`
- **中等数据量** (1000-10000 条): `batch_size: 100-200`
- **大数据量** (> 10000 条): `batch_size: 200-500`

### 2. 超时时间设置

- **快速任务**: `timeout: 300` (5分钟)
- **常规任务**: `timeout: 1800` (30分钟)
- **大批量任务**: `timeout: 7200` (2小时)

### 3. 内存优化

- 使用流式查询，避免一次性加载所有数据
- 合理设置批处理大小，平衡内存使用和性能
- 及时释放不再使用的对象

### 4. API 调用优化

- 在批处理之间添加适当的延迟，避免 API 限流
- 实现指数退避重试机制
- 监控 API 响应时间，及时调整调用频率

## 故障排查

### Function Compute 特定问题

#### 1. 函数冷启动延迟

**现象**: 函数首次执行或长时间未执行后响应较慢

**解决方案**:
- 配置预留实例保持函数热启动
- 优化代码减少启动时间
- 使用更大的内存配置

#### 2. 网络访问失败

**现象**: 函数无法访问数据库或外部API

**解决方案**:
- 检查VPC配置是否正确
- 验证安全组规则
- 确认网络连通性

#### 3. 时区问题

**现象**: 定时任务执行时间与预期不符

**解决方案**:
- Function Compute 使用UTC时间
- 北京时间需要在CRON表达式中减8小时
- 可以使用 `CRON_TZ=Asia/Shanghai` 指定时区

#### 4. 函数超时

**现象**: 函数执行时间超过配置的timeout值

**解决方案**:
- 增加函数的超时配置（最大15分钟）
- 优化任务逻辑，减少执行时间
- 考虑将大任务拆分为多个小任务

#### 5. 触发器未执行

**现象**: 定时触发器到时间未执行函数

**解决方案**:
- 检查触发器是否启用 (`enable: true`)
- 验证CRON表达式语法
- 查看触发器日志确认触发状态
- 检查函数是否有权限被触发

### 常见问题

#### 1. 数据库连接失败

**错误**: `数据库连接超时`

**解决方案**:
- 检查数据库服务是否正常运行
- 验证数据库连接参数
- 检查网络连接
- 增加连接超时时间

#### 2. API 调用失败

**错误**: `TrendInsight API 不可用`

**解决方案**:
- 检查 TrendInsight 服务状态
- 验证 API 凭证和权限
- 检查 cookies 是否过期
- 减少并发调用频率

#### 3. 内存不足

**错误**: `MemoryError` 或进程被 OOM Killer 终止

**解决方案**:
- 减少批处理大小
- 增加系统内存
- 检查是否有内存泄漏
- 使用流式处理替代批量加载

#### 4. 任务超时

**错误**: 任务执行超时，退出码 3

**解决方案**:
- 增加超时时间配置
- 减少批处理大小
- 优化数据库查询
- 检查网络延迟

### 调试模式

设置环境变量启用调试模式：

```bash
export LOG_LEVEL=DEBUG
python tasks/main.py '{"task_type": "trend_refresh", "batch_size": 10}'
```

### 健康检查

创建健康检查脚本：

```bash
#!/bin/bash
# health_check.sh

# 检查最近一小时是否有任务执行成功
if grep -q '"status": "success"' /var/log/trend-refresh.log | tail -n 100; then
    echo "OK: 任务执行正常"
    exit 0
else
    echo "ERROR: 最近没有成功的任务执行"
    exit 1
fi
```

## 扩展开发

### 添加新任务类型

1. 创建新的任务类继承 `BaseTask`
2. 在 `TaskConfig` 中添加新的任务类型验证
3. 在 `main.py` 中注册新任务
4. 编写相应的测试用例

示例：

```python
class CustomTask(BaseTask):
    async def execute(self) -> TaskResult:
        # 实现自定义任务逻辑
        pass

# 在 main.py 中注册
manager.register_task("custom_task", CustomTask)
```

### 添加监控指标

扩展 `TaskLogger` 添加自定义指标：

```python
class EnhancedTaskLogger(TaskLogger):
    def log_custom_metric(self, metric_name: str, value: float):
        metric_info = {
            "event": "metric",
            "metric_name": metric_name,
            "value": value,
            "timestamp": datetime.now().isoformat()
        }
        self.logger.info(json.dumps(metric_info, ensure_ascii=False))
```

## 相关文档

- [设计文档](../../../.kiro/specs/scheduled-trend-refresh/design.md)
- [需求文档](../../../.kiro/specs/scheduled-trend-refresh/requirements.md)
- [任务规划](../../../.kiro/specs/scheduled-trend-refresh/tasks.md)
- [TrendInsight 控制器文档](../../controllers/trendinsight.py)
- [数据库模型文档](../../models/trendinsight/models.py)